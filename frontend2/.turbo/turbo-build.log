

> frontend2@0.1.0 build
> next build

[1G[0K   [1m[38;2;173;127;168m▲ Next.js 15.3.3[39m[22m
   - Environments: .env.local, .env.production
   - Experiments (use with caution):
     [1m✓[22m optimizeCss

 [37m[1m [22m[39m Creating an optimized production build ...
 [32m[1m✓[22m[39m Compiled successfully in 2000ms
 [37m[1m [22m[39m Skipping validation of types
 [37m[1m [22m[39m Skipping linting
[?25l [37m[1m [22m[39m Collecting page data  [36m.[39m[2K[1G [37m[1m [22m[39m Collecting page data  [36m..[39m[2K[1G [37m[1m [22m[39m Collecting page data  [36m...[39m[2K[1G[?25h [37m[1m [22m[39m Collecting page data    
 [32m[1m✓[22m[39m Collecting page data 
[?25l [37m[1m [22m[39m Generating static pages (0/42)  [36m[    ][39m[2K[1G [37m[1m [22m[39m Generating static pages (0/42)  [36m[=   ][39m[2K[1G [37m[1m [22m[39m Generating static pages (0/42)  [36m[==  ][39m[2K[1G [37m[1m [22m[39m Generating static pages (29/42)  [36m[=== ][39m[2K[1G[?25h [32m[1m✓[22m[39m Generating static pages (42/42)
[?25l [37m[1m [22m[39m Finalizing page optimization  [36m.[39m[?25l [37m[1m [22m[39m Collecting build traces  [36m.[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m..[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m..[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m...[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m...[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m.[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m.[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m..[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m..[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m...[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m...[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m.[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m.[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m..[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m..[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m...[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m...[39m[2K[1G [37m[1m [22m[39m Finalizing page optimization  [36m.[39m[2K[1G [37m[1m [22m[39m Collecting build traces  [36m.[39m[2K[1G[?25h [37m[1m [22m[39m Collecting build traces    
 [32m[1m✓[22m[39m Collecting build traces 
[2K[1G[?25h [37m[1m [22m[39m Finalizing page optimization    
 [32m[1m✓[22m[39m Finalizing page optimization 

[4mRoute (app)[24m                                [4mSize[24m  [4mFirst Load JS[24m  [4m[24m  [4m[24m
┌ ○ /                                   6.32 kB         [37m[1m293 kB[22m[39m
├ ○ /_not-found                           201 B         [37m[1m287 kB[22m[39m
├ ○ /admin                               3.1 kB         [37m[1m290 kB[22m[39m
├ ○ /admin/dashboard                    2.58 kB         [37m[1m289 kB[22m[39m
├ ○ /admin/global-data                  3.36 kB         [37m[1m290 kB[22m[39m
├ ○ /admin/roles                         2.9 kB         [37m[1m290 kB[22m[39m
├ ○ /admin/services                     2.28 kB         [37m[1m289 kB[22m[39m
├ ○ /admin/settings                      3.3 kB         [37m[1m290 kB[22m[39m
├ ○ /admin/species-breeds               2.24 kB         [37m[1m289 kB[22m[39m
├ ○ /admin/users                        2.85 kB         [37m[1m290 kB[22m[39m
├ ○ /analytics                          2.92 kB         [37m[1m290 kB[22m[39m
├ ○ /appointments                       5.36 kB         [37m[1m292 kB[22m[39m
├ ƒ /appointments/[id]                  4.41 kB         [37m[1m291 kB[22m[39m
├ ƒ /appointments/[id]/tasks            3.57 kB         [37m[1m290 kB[22m[39m
├ ○ /appointments/calendar              3.19 kB         [37m[1m290 kB[22m[39m
├ ○ /appointments/new                   5.48 kB         [37m[1m292 kB[22m[39m
├ ○ /auth/login                         3.67 kB         [37m[1m290 kB[22m[39m
├ ○ /auth/register                       4.7 kB         [37m[1m291 kB[22m[39m
├ ○ /billing                            2.47 kB         [37m[1m289 kB[22m[39m
├ ○ /billing/invoices                    2.9 kB         [37m[1m290 kB[22m[39m
├ ○ /billing/invoices/new               3.32 kB         [37m[1m290 kB[22m[39m
├ ○ /billing/payments                   2.85 kB         [37m[1m290 kB[22m[39m
├ ○ /billing/reports                    2.67 kB         [37m[1m289 kB[22m[39m
├ ○ /clients                            2.62 kB         [37m[1m289 kB[22m[39m
├ ƒ /clients/[id]                       2.16 kB         [37m[1m289 kB[22m[39m
├ ○ /clients/add                        3.29 kB         [37m[1m290 kB[22m[39m
├ ƒ /clients/edit/[id]                  2.45 kB         [37m[1m289 kB[22m[39m
├ ○ /clinics                            2.98 kB         [37m[1m290 kB[22m[39m
├ ƒ /clinics/[id]                       5.74 kB         [37m[1m292 kB[22m[39m
├ ○ /clinics/add                        2.23 kB         [37m[1m289 kB[22m[39m
├ ƒ /clinics/edit/[id]                   3.2 kB         [37m[1m290 kB[22m[39m
├ ƒ /clinics/view/[id]                  4.84 kB         [37m[1m292 kB[22m[39m
├ ○ /dashboard                          3.53 kB         [37m[1m290 kB[22m[39m
├ ○ /login                              2.36 kB         [37m[1m289 kB[22m[39m
├ ○ /medical-records                    3.13 kB         [37m[1m290 kB[22m[39m
├ ƒ /medical-records/[id]               2.61 kB         [37m[1m289 kB[22m[39m
├ ○ /medical-records/new                1.64 kB         [37m[1m288 kB[22m[39m
├ ○ /notifications                      2.91 kB         [37m[1m290 kB[22m[39m
├ ○ /patients                           3.34 kB         [37m[1m290 kB[22m[39m
├ ○ /patients/add                        2.4 kB         [37m[1m289 kB[22m[39m
├ ƒ /patients/edit/[id]                 1.96 kB         [37m[1m289 kB[22m[39m
├ ○ /profile                            3.13 kB         [37m[1m290 kB[22m[39m
├ ○ /register                           2.98 kB         [37m[1m290 kB[22m[39m
├ ○ /staff                              2.71 kB         [37m[1m289 kB[22m[39m
├ ○ /staff/add                           2.2 kB         [37m[1m289 kB[22m[39m
├ ƒ /staff/edit/[id]                     2.4 kB         [37m[1m289 kB[22m[39m
├ ○ /staff/technicians                  3.05 kB         [37m[1m290 kB[22m[39m
├ ○ /staff/veterinarians                2.87 kB         [37m[1m290 kB[22m[39m
└ ○ /verify-email                       1.91 kB         [37m[1m289 kB[22m[39m
+ First Load JS shared by all            [37m[1m287 kB[22m[39m
  ├ chunks/common-bac3fa60740d2ce3.js   18.7 kB
  └ chunks/vendors-0fda4b04a8b27604.js   266 kB
  └ other shared chunks (total)         1.98 kB


○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

[?25h[1G[0K⠙[1G[0K
