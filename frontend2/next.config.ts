import type { NextConfig } from "next";
import path from "path";

// Bundle analyzer setup
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig: NextConfig = {
  eslint: {
    // Disable ESLint during builds temporarily
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Disable TypeScript errors during builds temporarily
    ignoreBuildErrors: true,
  },

  // Production optimizations
  compress: true,
  poweredByHeader: false,

  // Reduce development noise
  logging: {
    fetches: {
      fullUrl: process.env.NODE_ENV === 'development',
    },
  },

  // Turbopack configuration (stable)
  turbopack: {
    // Enable all Turbopack features for maximum performance
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
      '*.{js,jsx,ts,tsx}': {
        loaders: ['swc-loader'],
        as: '*.js',
      },
    },
    // Preload critical modules
    resolveAlias: {
      '@': './src',
      '@/components': './src/components',
      '@/lib': './src/lib',
      '@/contexts': './src/contexts',
    },
  },

  // Performance optimizations
  experimental: {
    // Performance optimizations - compatible with stable Next.js
    optimizePackageImports: [
      'lucide-react',
      'framer-motion',
      '@heroicons/react',
      'react-icons',
      '@headlessui/react',
      'react-hook-form',
      'zustand'
    ],
    optimizeCss: true,

    // Enable faster compilation
    esmExternals: true,

    // Faster builds and hot reloading
    webVitalsAttribution: ['CLS', 'LCP', 'FID', 'FCP'],
  },

  // Server external packages (moved from experimental)
  serverExternalPackages: ['sharp'],

  // Disable development error overlay (reduces network requests)
  devIndicators: {
    position: 'bottom-left',
  },

  // Disable React strict mode in development to reduce double renders and network calls
  reactStrictMode: process.env.NODE_ENV === 'production',

  // Optimize images
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Webpack optimizations (compatible with Turbo)
  webpack: (config, { dev, isServer }) => {
    // Only apply webpack config when not using Turbopack
    const isTurbo = process.env.TURBOPACK_ENABLED === 'true' || process.argv.includes('--turbo');

    if (isTurbo) {
      // Minimal webpack config for Turbo compatibility
      return config;
    }

    // Production optimizations (webpack only)
    if (!dev) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            enforce: true,
          },
        },
      };

      // Tree shaking optimizations
      config.optimization.usedExports = true;
      config.optimization.sideEffects = false;
    }

    // Development optimizations (webpack only)
    if (dev) {
      config.infrastructureLogging = {
        level: 'error',
      };

      // Reduce webpack dev server noise
      config.stats = 'errors-warnings';

      // Enable faster source maps
      config.devtool = 'eval-cheap-module-source-map';

      // Optimize module resolution for faster builds
      config.resolve.symlinks = false;
    }

    // SVG handling optimization (webpack only)
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },

  // Headers for performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },
};

export default withBundleAnalyzer(nextConfig);
